## Spayfall: <PERSON><PERSON> Dokümantasyonu

---

### 1. <PERSON><PERSON><PERSON>ış

Spayfall, oyuncuların gizli roller ve konumlar atandığı bir aldatma oyunudur. <PERSON><PERSON>, her turda belirli bir süre (örn. 6-10 dakika) süren birkaç turda oynanır.

### 2. Oyuncu Rolleri

* **Sivil (Casus Olmayan):** Oyuncuların çoğu sivil olacaktır. Gizli konumu ve o konumdaki belirli rollerini (örn. "restoranda" "şef") bilirler. Amaçları, konumu casusa çok açık etmeden casusu belirlemek için sorular sormaktır.
* **Casus:** Bir oyuncu casustur. Casus gizli konumu *bilmez*. Amacı, sorular ve cevaplar aracılığıyla ipuçları toplayarak konumu tahmin etmeye çalışırken, uyum sağlamak ve tespit edilmekten kaçınmaktır.

### 3. <PERSON><PERSON> (Mobil Uygulama)

1.  **Oyuncu Sayısı:** Oyun genellikle 4 ila 12 oyuncuyu destekler. Mobil uygulama, ana bilgisayarın oyuncu sayısını belirlemesine izin vermelidir.
2.  **Oda Oluşturma/Katılma:**
    * **Ana Bilgisayar:** Bir oyuncu bir "oda" veya "oyun oturumu" oluşturur. Uygulama benzersiz bir oda kodu oluşturur.
    * **Oyuncular:** Diğer oyuncular oda kodunu girerek odaya katılırlar.
3.  **Konum Havuzu:** Uygulamanın önceden tanımlanmış bir konum listesi olmalıdır (örn. "Kumarhane," "Uzay İstasyonu," "Restoran," "Banka," "Korsan Gemisi," "Hastane," "Üniversite," "Film Stüdyosu," "Elçilik," "Askeri Üs," "Polis Karakolu," "Gemi Seyahati"). Her konum için ilişkili rol listesi olmalıdır (örn. "Restoran" için: "Şef," "Garson," "Müşteri," "Yönetici").
    * **Yerelleştirme:** Konum ve rol adlarını hem Türkçe hem de İngilizce olarak saklayın.
4.  **Rol Ataması:** Her turun başında:
    * Havuzdan rastgele bir konum seçilir.
    * Bu konum için bir oyuncu rastgele "Casus" rolüne atanır.
    * Diğer tüm oyuncular seçilen konumla ilgili belirli bir "Sivil" rolüne atanır.
    * Oyunculara, gizliliği sağlayarak, cihaz ekranlarında atanan rolleri ve varsa konumları gösterilir.
5.  **Zamanlayıcı:** Ana bilgisayar bir tur zamanlayıcısı ayarlayabilir (örn. 8 dakika). Uygulama, oyun sırasında bir geri sayım zamanlayıcısı göstermelidir.

### 4. Oyun Akışı

1.  **Turu Başlatma:** Tüm oyuncular katıldıktan ve roller atandıktan sonra, ana bilgisayar turu başlatır. Bir zamanlayıcı geri saymaya başlar.
2.  **Sorgulama Aşaması:**
    * Bir oyuncu (örn. ana bilgisayar veya rastgele seçilen bir oyuncu) başka bir oyuncuya soru sorarak başlar.
    * Sorular mevcut konum hakkında olmalıdır.
    * Örnek: "Burası genellikle üniforma giyeceğiniz bir yer mi?" veya "Burada çok hayvan var mı?"
    * **Casusun Zorluğu:** Casus, konum hakkındaki bilgisizliğini açığa vurmadan sorulara kaçamak yanıtlar vermeli, aynı zamanda sorular ve cevaplardan ipuçları toplamaya çalışarak konumu tahmin etmelidir.
    * **Sivilin Zorluğu:** Siviller, casusu (konumu bilmeyen) sivilden ayıracak sorular sormalı, ancak casusun konumu hemen tahmin etmesine neden olacak kadar bariz olmamalıdır.
    * **Sıra Düzeni:** Bir oyuncu bir soruyu cevapladıktan sonra, başka bir oyuncuya soru sorma sırası kendilerine gelir (kendilerine soru soran oyuncuya değil). Bu, zamanlayıcı bitene veya bir suçlama yapılana kadar devam eder.
    * **"İntikam Yok" Kuralı:** Bir oyuncu, kendisine az önce soru soran kişiye hemen geri soru soramaz. Başka birini seçmeleri gerekir.
3.  **Suçlama / Oylama:**
    * Tur sırasında herhangi bir noktada, herhangi bir oyuncu **saati durdurup başka bir oyuncuyu casus olmakla suçlamayı** seçebilir.
    * **Oylama:** Bir suçlama yapılırsa, diğer tüm oyuncular (suçlanan hariç) suçlamaya katılıp katılmadıklarını oylamalıdır.
    * **Oy Birliği:** Suçlamanın başarılı olması için, şüpheli casusu suçlama oylamasının, suçlanmayan tüm oyuncular arasında *oy birliğiyle* yapılması gerekir.
    * **Suçlamanın Sonucu:**
        * Oylama oy birliğiyle yapılır ve suçlanan *casus ise*: **Casus olmayanlar turu kazanır.** Casus kartını açar.
        * Oylama oy birliğiyle yapılır ve suçlanan *casus değilse*: **Casus turu kazanır.** Yanlış suçlanan sivil kartını açar.
        * Oylama *oy birliğiyle yapılmazsa*: Tur devam eder ve saat yeniden başlar. Başarısız suçlama yapan oyuncu o turda başka bir suçlama yapamaz.
    * **Tur Başına Bir Suçlama:** Her oyuncu tur başına yalnızca bir kez suçlama başlatabilir.

### 5. Tur Sonu Koşulları

Bir tur, aşağıdakilerden biri gerçekleştiğinde sona erer:

1.  **Zamanlayıcı Biter:** Zamanlayıcı dolarsa, oyuncular tartışmalı ve ardından casus olduğundan şüphelendikleri kişiyi oylamalıdır. Dağıtıcı (veya ana bilgisayar) saat yönünde bir oylama ister.
    * Tüm oyuncular (casus hariç) doğru casusu başarıyla oylarsa, casus olmayanlar kazanır.
    * Oylama oy birliğiyle yapılmazsa veya bir casus olmayan yanlışlıkla suçlanırsa, casus kazanır.
2.  **Başarılı Suçlama:** Bir oyuncu başarıyla suçlanır ve oy birliğiyle casus olarak oylanır (ve gerçekten casustur). **Casus olmayanlar kazanır.**
3.  **Casus Konumu Tahmin Eder:** Casus, istediği zaman saati durdurabilir ve **konumu tahmin etmeye çalışabilir**.
    * Casus konumu doğru tahmin ederse: **Casus turu kazanır.**
    * Casus yanlış tahmin ederse: **Casus olmayanlar turu kazanır.**
    * **Önemli:** Başka bir oyuncu zaten bir suçlama için saati durdurmuşsa, casus konumu tahmin etme şansını kaybeder.

### 6. Puanlama ve Kazanma

Oyun genellikle birkaç turda (örn. 3-5 tur) oynanır ve her tur sonunda puanlar verilir. Oyun sonunda en çok puanı olan oyuncu kazanır.

* **Casus Kazanır:**
    * Kimse başarıyla casus olarak suçlanmazsa (zaman biter veya suçlama başarısız olursa) 2 puan.
    * Casus olmayan bir oyuncu başarıyla suçlanırsa 4 puan.
    * Casus oyunu durdurur ve konumu başarıyla tahmin ederse 4 puan.
* **Casus Olmayanlar Kazanır:**
    * Casusu başarıyla suçlarlar ve oylarlarsa her casus olmayan oyuncu için 1 puan. Başarılı suçlamayı başlatan oyuncu ek olarak 1 puan alır (toplam 2 puan).

### 7. Teknik Gereksinimler ve Tasarım İlkeleri

* **Veritabanı (DB):** Gerçek zamanlı oyun durumu senkronizasyonu, oyuncu verileri ve oda yönetimi için **Firebase Realtime Database veya Firestore** kullanılması şiddetle önerilir. Firebase'in sunduğu kolay entegrasyon ve ölçeklenebilirlik, çok oyunculu bir oyun için idealdir.
* **Kullanıcı Arayüzü (UI) ve Kullanıcı Deneyimi (UX) Kuralları:**
    * **Sezgisel Gezinme:** Oyuncular oyun içinde kolayca gezinebilmeli ve gerekli bilgilere hızla ulaşabilmelidir.
    * **Anlaşılır Geri Bildirim:** Her eylem (örn. soru sorma, oylama) için net görsel ve/veya işitsel geri bildirim sağlanmalıdır.
    * **Tutarlı Tasarım:** Uygulama genelinde renkler, yazı tipleri, düğmeler ve simgeler tutarlı bir tasarım diline sahip olmalıdır.
    * **Erişilebilirlik:** Renk körlüğü, daha büyük metin seçenekleri gibi erişilebilirlik özellikleri göz önünde bulundurulmalıdır.
* **Profesyonel, Modern ve Oyun Temasına Uygun Tasarım:**
    * **Görsel Tema:** Spayfall'ın gizem, entrika ve gerilim temasına uygun, şık ve modern bir tasarım dili benimsenmelidir. Koyu tonlar, vurgulu renkler ve minimalist öğeler kullanılabilir.
    * **Oyun Atmosferi:** UI elemanları (düğmeler, kartlar, zamanlayıcı) oyunun genel atmosferini yansıtmalıdır. Örneğin, rol kartları veya konum kartları oyundaki gizemi ve beklenmedikliği yansıtan tasarımlara sahip olabilir.
    * **Akıcı Animasyonlar:** Geçişler ve etkileşimler arasında akıcı animasyonlar kullanılarak kullanıcı deneyimi zenginleştirilmelidir.
    * **Ses Efektleri:** Oyunun kritik anlarını (örn. tur başlama, casusun yakalanması, yanlış suçlama) vurgulayan uygun ses efektleri eklenebilir.
* **Temel Özellikler (Flutter Geliştirme):**
    * Mevcut rolün (Sivil/Casus) ve konumun net bir şekilde gösterimi.
    * Soru sormak ve suçlamaları/oylamaları başlatmak için sezgisel kontroller.
    * Geri sayım zamanlayıcısı gösterimi.
    * Durumları (örn. "çevrimiçi," "suçlu") gösteren oyuncu listesi.
    * Casusun tahmin ederken seçebileceği konum listesi.
    * Her turun ve oyunun sonunda puan tablosu.
* **Çok Oyunculu İşlevsellik:**
    * Gerçek zamanlı iletişim için sohbet veya soru/cevap arayüzü.
    * Güvenli oda oluşturma ve katılma.
    * Oyun durumunun (zamanlayıcı, oylar, suçlamalar) senkronizasyonu.
* **Yerelleştirme (i18n):**
    * Tüm UI öğeleri, oyun kuralları, roller ve konumlar için İngilizce ve Türkçe metin desteği.
    * Uygulama ayarlarında diller arasında kolay geçiş.
* **Oyun Mantığı:**
    * Rollerin ve konumların sağlam rastgele atanması.
    * Doğru zamanlayıcı yönetimi.
    * Oy birliği kontrolleri de dahil olmak üzere suçlama ve oylama mekanizmalarının doğru şekilde ele alınması.
    * Doğru puanlama sistemi.
* **Moderasyon Modu (İsteğe bağlı ancak çevrimiçi oyun için önerilir):**
    * Ana bilgisayar turları yeniden başlatabilir, zamanlayıcıları ayarlayabilir veya oyları geçersiz kılabilir.
    * Casus sayısını oyunculardan gizleme seçeneği.

### 8. Potansiyel Geliştirmeler / Gelecek Özellikler

* **Özel Konumlar ve Roller:** Oyuncuların kendi özel konumlarını ve rollerini oluşturmalarına ve paylaşmalarına izin verin.
* **Temalı Paketler:** Konumlar ve roller için farklı temalar tanıtın.
* **Eğitim:** Yeni oyuncuları kurallar hakkında yönlendirmek için uygulama içi bir eğitim.