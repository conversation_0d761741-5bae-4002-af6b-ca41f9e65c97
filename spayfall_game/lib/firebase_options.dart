// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBVHtoCduDY85WxEAn8XqX5qanRF2Kpqj4',
    authDomain: 'whoisthespy-a1740.firebaseapp.com',
    databaseURL: 'https://whoisthespy-a1740-default-rtdb.europe-west1.firebasedatabase.app',
    projectId: 'whoisthespy-a1740',
    storageBucket: 'whoisthespy-a1740.firebasestorage.app',
    messagingSenderId: '1063356725922',
    appId: '1:1063356725922:web:5254b2fdb2a6f3b8da75fb',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBVHtoCduDY85WxEAn8XqX5qanRF2Kpqj4',
    appId: '1:1063356725922:android:demo',
    messagingSenderId: '1063356725922',
    projectId: 'whoisthespy-a1740',
    databaseURL: 'https://whoisthespy-a1740-default-rtdb.europe-west1.firebasedatabase.app',
    storageBucket: 'whoisthespy-a1740.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBVHtoCduDY85WxEAn8XqX5qanRF2Kpqj4',
    appId: '1:1063356725922:ios:demo',
    messagingSenderId: '1063356725922',
    projectId: 'whoisthespy-a1740',
    databaseURL: 'https://whoisthespy-a1740-default-rtdb.europe-west1.firebasedatabase.app',
    storageBucket: 'whoisthespy-a1740.firebasestorage.app',
    iosBundleId: 'com.spayfall.spayfallGame',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBVHtoCduDY85WxEAn8XqX5qanRF2Kpqj4',
    appId: '1:1063356725922:macos:demo',
    messagingSenderId: '1063356725922',
    projectId: 'whoisthespy-a1740',
    databaseURL: 'https://whoisthespy-a1740-default-rtdb.europe-west1.firebasedatabase.app',
    storageBucket: 'whoisthespy-a1740.firebasestorage.app',
    iosBundleId: 'com.spayfall.spayfallGame',
  );
}
