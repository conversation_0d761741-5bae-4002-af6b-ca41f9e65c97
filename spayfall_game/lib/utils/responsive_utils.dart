import 'package:flutter/material.dart';

class ResponsiveUtils {
  static bool isSmallScreen(BuildContext context) {
    return MediaQuery.of(context).size.height < 700;
  }
  
  static bool isVerySmallScreen(BuildContext context) {
    return MediaQuery.of(context).size.height < 600;
  }
  
  static bool isNarrowScreen(BuildContext context) {
    return MediaQuery.of(context).size.width < 400;
  }
  
  static double getResponsivePadding(BuildContext context) {
    if (isVerySmallScreen(context)) return 6.0;
    if (isSmallScreen(context)) return 8.0;
    return 12.0;
  }

  static double getResponsiveSpacing(BuildContext context) {
    if (isVerySmallScreen(context)) return 6.0;
    if (isSmallScreen(context)) return 8.0;
    return 12.0;
  }

  static double getTimerFontSize(BuildContext context) {
    if (isVerySmallScreen(context)) return 20.0;
    if (isSmallScreen(context)) return 24.0;
    return 30.0;
  }

  static double getTimerPadding(BuildContext context) {
    if (isVerySmallScreen(context)) return 8.0;
    if (isSmallScreen(context)) return 12.0;
    return 16.0;
  }

  static double getRoleCardFontSize(BuildContext context) {
    if (isVerySmallScreen(context)) return 18.0;
    if (isSmallScreen(context)) return 20.0;
    return 24.0;
  }
  
  static double getButtonPadding(BuildContext context) {
    if (isVerySmallScreen(context)) return 12.0;
    if (isSmallScreen(context)) return 14.0;
    return 16.0;
  }
  
  static double getPlayerListPadding(BuildContext context) {
    if (isVerySmallScreen(context)) return 8.0;
    if (isSmallScreen(context)) return 12.0;
    return 16.0;
  }

  static double getDialogHeight(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    if (isVerySmallScreen(context)) return screenHeight * 0.6;
    if (isSmallScreen(context)) return screenHeight * 0.7;
    return 400.0;
  }

  static EdgeInsets getDialogPadding(BuildContext context) {
    if (isVerySmallScreen(context)) return const EdgeInsets.all(8.0);
    if (isSmallScreen(context)) return const EdgeInsets.all(12.0);
    return const EdgeInsets.all(16.0);
  }
}
