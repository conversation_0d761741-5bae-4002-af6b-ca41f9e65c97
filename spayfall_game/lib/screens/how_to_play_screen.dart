import 'package:flutter/material.dart';
import '../l10n/app_localizations.dart';

class HowToPlayScreen extends StatelessWidget {
  const HowToPlayScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.howToPlay),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0F0F23),
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
            ],
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title
              Center(
                child: Text(
                  l10n.howToPlayTitle,
                  style: const TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Game Objective
              _buildSection(
                title: l10n.gameObjective,
                content: l10n.gameObjectiveText,
                icon: Icons.flag,
              ),
              
              const SizedBox(height: 24),
              
              // Civilian Objective
              _buildSection(
                title: l10n.civilianObjective,
                content: l10n.civilianObjectiveText,
                icon: Icons.people,
                color: const Color(0xFF4CAF50),
              ),
              
              const SizedBox(height: 24),
              
              // Spy Objective
              _buildSection(
                title: l10n.spyObjective,
                content: l10n.spyObjectiveText,
                icon: Icons.visibility_off,
                color: const Color(0xFFFF5722),
              ),
              
              const SizedBox(height: 24),
              
              // Game Setup
              _buildSection(
                title: l10n.gameSetup,
                content: l10n.gameSetupText,
                icon: Icons.settings,
              ),
              
              const SizedBox(height: 24),
              
              // Game Flow
              _buildSection(
                title: l10n.gameFlow,
                content: l10n.gameFlowText,
                icon: Icons.play_arrow,
              ),
              
              const SizedBox(height: 24),
              
              // Question Phase
              _buildSection(
                title: l10n.questionPhase,
                content: l10n.questionPhaseText,
                icon: Icons.help_outline,
                color: const Color(0xFF2196F3),
              ),
              
              const SizedBox(height: 24),
              
              // Accusation Phase
              _buildSection(
                title: l10n.accusationPhase,
                content: l10n.accusationPhaseText,
                icon: Icons.gavel,
                color: const Color(0xFFFF9800),
              ),
              
              const SizedBox(height: 24),
              
              // Win Conditions
              _buildSection(
                title: l10n.winConditions,
                content: l10n.winConditionsText,
                icon: Icons.emoji_events,
                color: const Color(0xFFFFD700),
              ),
              
              const SizedBox(height: 24),
              
              // Tips
              _buildSection(
                title: l10n.tips,
                content: l10n.tipsText,
                icon: Icons.lightbulb_outline,
                color: const Color(0xFF9C27B0),
              ),
              
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required String content,
    required IconData icon,
    Color? color,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: color ?? const Color(0xFF0E4B99),
                size: 28,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: color ?? const Color(0xFF0E4B99),
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          Text(
            content,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white,
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }
}
