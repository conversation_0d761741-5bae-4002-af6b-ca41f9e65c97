import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/analytics_service.dart';
import '../l10n/app_localizations.dart';

class AnalyticsTestScreen extends StatelessWidget {
  const AnalyticsTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final analyticsService = Provider.of<AnalyticsService>(context, listen: false);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Analytics Test'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: Colors.white,
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0F0F23),
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
            ],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const Text(
                  'Firebase Analytics Test',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 32),
                
                ElevatedButton(
                  onPressed: () async {
                    await analyticsService.logGameCreated(
                      roomCode: 'TEST123',
                      playerCount: 4,
                    );
                    _showSnackBar(context, 'Game Created event logged');
                  },
                  style: _buttonStyle(),
                  child: const Text('Log Game Created'),
                ),
                
                const SizedBox(height: 16),
                
                ElevatedButton(
                  onPressed: () async {
                    await analyticsService.logGameJoined(
                      roomCode: 'TEST123',
                      playerCount: 5,
                    );
                    _showSnackBar(context, 'Game Joined event logged');
                  },
                  style: _buttonStyle(),
                  child: const Text('Log Game Joined'),
                ),
                
                const SizedBox(height: 16),
                
                ElevatedButton(
                  onPressed: () async {
                    await analyticsService.logGameStarted(
                      roomCode: 'TEST123',
                      playerCount: 5,
                      location: 'airport',
                    );
                    _showSnackBar(context, 'Game Started event logged');
                  },
                  style: _buttonStyle(),
                  child: const Text('Log Game Started'),
                ),
                
                const SizedBox(height: 16),
                
                ElevatedButton(
                  onPressed: () async {
                    await analyticsService.logPlayerAccused(
                      roomCode: 'TEST123',
                      accuserRole: 'civilian',
                      accusedRole: 'spy',
                      roundNumber: 1,
                    );
                    _showSnackBar(context, 'Player Accused event logged');
                  },
                  style: _buttonStyle(),
                  child: const Text('Log Player Accused'),
                ),
                
                const SizedBox(height: 16),
                
                ElevatedButton(
                  onPressed: () async {
                    await analyticsService.logVoteCast(
                      roomCode: 'TEST123',
                      voterRole: 'civilian',
                      voteYes: true,
                      roundNumber: 1,
                    );
                    _showSnackBar(context, 'Vote Cast event logged');
                  },
                  style: _buttonStyle(),
                  child: const Text('Log Vote Cast'),
                ),
                
                const SizedBox(height: 16),
                
                ElevatedButton(
                  onPressed: () async {
                    await analyticsService.logLocationGuessed(
                      roomCode: 'TEST123',
                      guessedLocation: 'airport',
                      actualLocation: 'airport',
                      correct: true,
                      roundNumber: 1,
                    );
                    _showSnackBar(context, 'Location Guessed event logged');
                  },
                  style: _buttonStyle(),
                  child: const Text('Log Location Guessed'),
                ),
                
                const SizedBox(height: 16),
                
                ElevatedButton(
                  onPressed: () async {
                    await analyticsService.logGameEnded(
                      roomCode: 'TEST123',
                      spyWins: true,
                      reason: 'Spy guessed location correctly',
                      roundNumber: 1,
                      gameDurationSeconds: 300,
                    );
                    _showSnackBar(context, 'Game Ended event logged');
                  },
                  style: _buttonStyle(),
                  child: const Text('Log Game Ended'),
                ),
                
                const SizedBox(height: 32),
                
                ElevatedButton(
                  onPressed: () async {
                    await analyticsService.setUserLanguage('en');
                    _showSnackBar(context, 'User language property set');
                  },
                  style: _buttonStyle(),
                  child: const Text('Set User Language'),
                ),
                
                const SizedBox(height: 16),
                
                ElevatedButton(
                  onPressed: () async {
                    await analyticsService.logScreenView('analytics_test_screen');
                    _showSnackBar(context, 'Screen view event logged');
                  },
                  style: _buttonStyle(),
                  child: const Text('Log Screen View'),
                ),
                
                const Spacer(),
                
                const Text(
                  'Check the console for analytics logs and Firebase console for events.',
                  style: TextStyle(
                    color: Colors.white60,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  ButtonStyle _buttonStyle() {
    return ElevatedButton.styleFrom(
      backgroundColor: const Color(0xFF0E4B99),
      foregroundColor: Colors.white,
      padding: const EdgeInsets.symmetric(vertical: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
    );
  }

  void _showSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFF0E4B99),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
