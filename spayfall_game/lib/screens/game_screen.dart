import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import '../services/firebase_service.dart';
import '../models/game_room.dart';
import '../models/player.dart';
import 'waiting_room_screen.dart';

import '../data/locations.dart';
import '../utils/responsive_utils.dart';
import '../l10n/app_localizations.dart';

class GameScreen extends StatefulWidget {
  final String roomId;
  final String playerId;

  const GameScreen({
    super.key,
    required this.roomId,
    required this.playerId,
  });

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> {
  GameRoom? _lastRoom;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final firebaseService = Provider.of<FirebaseService>(context, listen: false);
    
    return Scaffold(
      appBar: AppBar(
        title: Text('${l10n.round} 1'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            _showExitDialog(context);
          },
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0F0F23),
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
            ],
          ),
        ),
        child: StreamBuilder<GameRoom?>(
          stream: firebaseService.watchRoom(widget.roomId),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0E4B99)),
                ),
              );
            }

            if (snapshot.hasError || !snapshot.hasData) {
              return const Center(
                child: Text(
                  'Connection error',
                  style: TextStyle(color: Colors.white),
                ),
              );
            }

            final room = snapshot.data!;

            // Sadece room değiştiğinde güncelle
            if (_lastRoom == null ||
                _lastRoom!.id != room.id ||
                _lastRoom!.state != room.state ||
                _lastRoom!.roundEndTime != room.roundEndTime ||
                _lastRoom!.players.length != room.players.length) {

              _lastRoom = room;
            }

            final currentPlayer = room.players.firstWhere(
              (p) => p.id == widget.playerId,
              orElse: () => room.players.first,
            );

            return SafeArea(
              child: Padding(
                padding: EdgeInsets.all(ResponsiveUtils.getResponsivePadding(context)),
                child: Column(
                  children: [
                    // Timer - Tamamen bağımsız widget
                    GameTimerWidget(
                      endTime: room.roundEndTime,
                      gameState: room.state,
                    ),
                    SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context)),

                    // Role Card
                    _buildRoleCard(context, currentPlayer, room, l10n),
                    SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context)),

                    // Game State Content
                    Expanded(
                      child: _buildGameContent(context, room, currentPlayer, firebaseService, l10n),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }



  Widget _buildRoleCard(BuildContext context, Player currentPlayer, GameRoom room, AppLocalizations l10n) {
    final isSpy = currentPlayer.role == PlayerRole.spy;
    final isSmallScreen = ResponsiveUtils.isSmallScreen(context);
    final isVerySmallScreen = ResponsiveUtils.isVerySmallScreen(context);

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(isVerySmallScreen ? 8.0 : isSmallScreen ? 10.0 : 14.0),
      decoration: BoxDecoration(
        color: isSpy ? const Color(0xFF8B0000) : const Color(0xFF0E4B99),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: (isSpy ? const Color(0xFF8B0000) : const Color(0xFF0E4B99)).withOpacity(0.3),
            blurRadius: 8,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                isSpy ? Icons.visibility_off : Icons.location_on,
                color: Colors.white,
                size: isVerySmallScreen ? 20 : 24,
              ),
              SizedBox(width: isVerySmallScreen ? 4 : 6),
              Text(
                l10n.yourRole,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.white70,
                  fontSize: isVerySmallScreen ? 12 : 14,
                ),
              ),
            ],
          ),
          SizedBox(height: isVerySmallScreen ? 4 : 6),
          Text(
            isSpy ? l10n.spy : l10n.civilian,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: ResponsiveUtils.getRoleCardFontSize(context),
            ),
          ),
          if (!isSpy && currentPlayer.specificRole != null && room.currentLocation != null) ...[
            SizedBox(height: isVerySmallScreen ? 2 : 4),
            Text(
              room.currentLocation!.translateRole(currentPlayer.specificRole!, l10n.localeName),
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.white70,
                fontSize: isVerySmallScreen ? 14 : null,
              ),
            ),
          ],
          if (!isSpy && room.currentLocation != null) ...[
            SizedBox(height: isVerySmallScreen ? 6 : 8),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: isVerySmallScreen ? 8 : 12,
                vertical: isVerySmallScreen ? 4 : 6
              ),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '${l10n.location}: ${room.currentLocation!.getName(l10n.localeName)}',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: isVerySmallScreen ? 12 : 14,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildGameContent(BuildContext context, GameRoom room, Player currentPlayer,
      FirebaseService firebaseService, AppLocalizations l10n) {

    if (room.state == GameState.voting) {
      return _buildVotingContent(context, room, currentPlayer, firebaseService, l10n);
    }

    if (room.state == GameState.roundEnd || room.state == GameState.gameEnd) {
      return _buildGameEndContent(context, room, currentPlayer, firebaseService, l10n);
    }

    return _buildPlayingContent(context, room, currentPlayer, firebaseService, l10n);
  }

  Widget _buildPlayingContent(BuildContext context, GameRoom room, Player currentPlayer,
      FirebaseService firebaseService, AppLocalizations l10n) {

    final isSpy = currentPlayer.role == PlayerRole.spy;
    final buttonPadding = ResponsiveUtils.getButtonPadding(context);
    final spacing = ResponsiveUtils.getResponsiveSpacing(context);

    return Column(
      children: [
        // Players List
        Expanded(
          flex: 2,
          child: _buildPlayersList(context, room, currentPlayer, l10n),
        ),

        SizedBox(height: spacing),

        // Action Buttons
        if (isSpy) ...[
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _showLocationGuessDialog(context, firebaseService, l10n),
              icon: const Icon(Icons.location_searching),
              label: Text(l10n.guessLocation),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF8B0000),
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: buttonPadding),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ] else ...[
          // Siviller için tüm konumları göster butonu
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _showAllLocationsDialog(context, l10n),
              icon: const Icon(Icons.list),
              label: Text(l10n.viewAllLocations),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0E4B99),
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: buttonPadding),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
          SizedBox(height: spacing * 0.5), // Smaller spacing between buttons
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: currentPlayer.hasAccused
                  ? null
                  : () => _showAccusationDialog(context, room, firebaseService, l10n),
              icon: const Icon(Icons.gavel),
              label: Text(l10n.accusePlayer),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0E4B99),
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: buttonPadding),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildVotingContent(BuildContext context, GameRoom room, Player currentPlayer,
      FirebaseService firebaseService, AppLocalizations l10n) {

    final accuser = room.players.firstWhere((p) => p.id == room.currentAccuserId);
    final accused = room.players.firstWhere((p) => p.id == room.accusedPlayerId);
    final canVote = currentPlayer.id != room.accusedPlayerId && !currentPlayer.hasVoted;
    final responsivePadding = ResponsiveUtils.getResponsivePadding(context);
    final spacing = ResponsiveUtils.getResponsiveSpacing(context);
    final buttonPadding = ResponsiveUtils.getButtonPadding(context);

    return Column(
      children: [
        // Accusation Info
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(responsivePadding),
          decoration: BoxDecoration(
            color: const Color(0xFF8B0000).withOpacity(0.2),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: const Color(0xFF8B0000)),
          ),
          child: Column(
            children: [
              Icon(
                Icons.gavel,
                color: const Color(0xFF8B0000),
                size: ResponsiveUtils.isVerySmallScreen(context) ? 24 : 28,
              ),
              SizedBox(height: ResponsiveUtils.isVerySmallScreen(context) ? 4 : 6),
              Text(
                l10n.accusation,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: ResponsiveUtils.isVerySmallScreen(context) ? 18 : 20,
                ),
              ),
              SizedBox(height: ResponsiveUtils.isVerySmallScreen(context) ? 4 : 6),
              RichText(
                textAlign: TextAlign.center,
                text: TextSpan(
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.white70,
                    fontSize: ResponsiveUtils.isVerySmallScreen(context) ? 13 : 15,
                  ),
                  children: [
                    TextSpan(text: accuser.name),
                    TextSpan(text: l10n.accusesText),
                    TextSpan(
                      text: accused.name,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF8B0000),
                      ),
                    ),
                    TextSpan(text: l10n.ofBeingSpyText),
                  ],
                ),
              ),
            ],
          ),
        ),

        SizedBox(height: ResponsiveUtils.isVerySmallScreen(context) ? 8 : 12),

        // Voting Status
        Expanded(
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.all(responsivePadding),
            decoration: BoxDecoration(
              color: const Color(0xFF16213E).withOpacity(0.5),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: const Color(0xFF0E4B99).withOpacity(0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  l10n.voting,
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Colors.white,
                    fontSize: ResponsiveUtils.isVerySmallScreen(context) ? 16 : 18,
                  ),
                ),
                SizedBox(height: ResponsiveUtils.isVerySmallScreen(context) ? 8 : 12),
                Expanded(
                  child: ListView.builder(
                    itemCount: room.players.length,
                    itemBuilder: (context, index) {
                      final player = room.players[index];
                      final isAccused = player.id == room.accusedPlayerId;
                      final hasVoted = room.votes.containsKey(player.id);
                      final vote = room.votes[player.id];

                      return Container(
                        margin: EdgeInsets.only(bottom: ResponsiveUtils.isVerySmallScreen(context) ? 4 : 6),
                        padding: EdgeInsets.all(ResponsiveUtils.isVerySmallScreen(context) ? 8 : 10),
                        decoration: BoxDecoration(
                          color: isAccused
                              ? const Color(0xFF8B0000).withOpacity(0.2)
                              : const Color(0xFF16213E),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            CircleAvatar(
                              radius: ResponsiveUtils.isVerySmallScreen(context) ? 12 : 14,
                              backgroundColor: isAccused
                                  ? const Color(0xFF8B0000)
                                  : const Color(0xFF0E4B99),
                              child: Text(
                                player.name[0].toUpperCase(),
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: ResponsiveUtils.isVerySmallScreen(context) ? 10 : 11,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            SizedBox(width: ResponsiveUtils.isVerySmallScreen(context) ? 8 : 10),
                            Expanded(
                              child: Text(
                                player.name,
                                style: TextStyle(
                                  color: isAccused ? const Color(0xFF8B0000) : Colors.white,
                                  fontWeight: isAccused ? FontWeight.bold : FontWeight.normal,
                                  fontSize: ResponsiveUtils.isVerySmallScreen(context) ? 13 : 14,
                                ),
                              ),
                            ),
                            if (isAccused)
                              Text(
                                l10n.accused,
                                style: TextStyle(
                                  color: const Color(0xFF8B0000),
                                  fontSize: ResponsiveUtils.isVerySmallScreen(context) ? 10 : 11,
                                  fontWeight: FontWeight.bold,
                                ),
                              )
                            else if (hasVoted)
                              Icon(
                                vote! ? Icons.thumb_up : Icons.thumb_down,
                                color: vote ? Colors.green : Colors.red,
                                size: ResponsiveUtils.isVerySmallScreen(context) ? 16 : 18,
                              )
                            else
                              Icon(
                                Icons.hourglass_empty,
                                color: Colors.grey,
                                size: ResponsiveUtils.isVerySmallScreen(context) ? 16 : 18,
                              ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),

        SizedBox(height: spacing),

        // Vote Buttons
        if (canVote) ...[
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _vote(firebaseService, true),
                  icon: const Icon(Icons.thumb_up),
                  label: Text(l10n.yes),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: buttonPadding),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              SizedBox(width: spacing),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _vote(firebaseService, false),
                  icon: const Icon(Icons.thumb_down),
                  label: Text(l10n.no),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: buttonPadding),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ] else if (currentPlayer.id == room.accusedPlayerId) ...[
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(ResponsiveUtils.isVerySmallScreen(context) ? 10 : 12),
            decoration: BoxDecoration(
              color: const Color(0xFF8B0000).withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              l10n.youAreAccused,
              style: TextStyle(
                color: Colors.white70,
                fontWeight: FontWeight.w600,
                fontSize: ResponsiveUtils.isVerySmallScreen(context) ? 13 : 14,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ] else ...[
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(ResponsiveUtils.isVerySmallScreen(context) ? 10 : 12),
            decoration: BoxDecoration(
              color: const Color(0xFF16213E).withOpacity(0.5),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              'You have already voted',
              style: TextStyle(
                color: Colors.white70,
                fontWeight: FontWeight.w600,
                fontSize: ResponsiveUtils.isVerySmallScreen(context) ? 13 : 14,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildPlayersList(BuildContext context, GameRoom room, Player currentPlayer, AppLocalizations l10n) {
    final listPadding = ResponsiveUtils.getPlayerListPadding(context);
    final isVerySmallScreen = ResponsiveUtils.isVerySmallScreen(context);

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(listPadding),
      decoration: BoxDecoration(
        color: const Color(0xFF16213E).withOpacity(0.5),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF0E4B99).withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.people,
                color: const Color(0xFF0E4B99),
                size: isVerySmallScreen ? 20 : 24,
              ),
              SizedBox(width: isVerySmallScreen ? 6 : 8),
              Text(
                l10n.players,
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: Colors.white,
                  fontSize: isVerySmallScreen ? 16 : 18,
                ),
              ),
            ],
          ),
          SizedBox(height: isVerySmallScreen ? 8 : 12),
          Expanded(
            child: ListView.builder(
              itemCount: room.players.length,
              itemBuilder: (context, index) {
                final player = room.players[index];
                final isCurrentPlayer = player.id == currentPlayer.id;

                return Container(
                  margin: EdgeInsets.only(bottom: isVerySmallScreen ? 6 : 8),
                  padding: EdgeInsets.all(isVerySmallScreen ? 8 : 12),
                  decoration: BoxDecoration(
                    color: isCurrentPlayer
                        ? const Color(0xFF0E4B99).withOpacity(0.3)
                        : const Color(0xFF16213E),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isCurrentPlayer
                          ? const Color(0xFF0E4B99)
                          : Colors.transparent,
                    ),
                  ),
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: isVerySmallScreen ? 14 : 16,
                        backgroundColor: const Color(0xFF0E4B99),
                        child: Text(
                          player.name[0].toUpperCase(),
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: isVerySmallScreen ? 10 : 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      SizedBox(width: isVerySmallScreen ? 8 : 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              player.name,
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                                fontSize: isVerySmallScreen ? 14 : null,
                              ),
                            ),
                            if (player.isHost)
                              Text(
                                'Host',
                                style: TextStyle(
                                  color: const Color(0xFF0E4B99),
                                  fontSize: isVerySmallScreen ? 8 : 10,
                                ),
                              ),
                          ],
                        ),
                      ),
                      Text(
                        '${player.score}',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: isVerySmallScreen ? 14 : null,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _showAccusationDialog(BuildContext context, GameRoom room, FirebaseService firebaseService, AppLocalizations l10n) {
    final otherPlayers = room.players.where((p) => p.id != widget.playerId).toList();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        title: Text(
          l10n.accusePlayer,
          style: const TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              l10n.selectPlayerToAccuse,
              style: const TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 16),
            ...otherPlayers.map((player) => ListTile(
              leading: CircleAvatar(
                backgroundColor: const Color(0xFF0E4B99),
                child: Text(
                  player.name[0].toUpperCase(),
                  style: const TextStyle(color: Colors.white),
                ),
              ),
              title: Text(
                player.name,
                style: const TextStyle(color: Colors.white),
              ),
              onTap: () {
                Navigator.pop(context);
                _accusePlayer(firebaseService, player.id);
              },
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.cancel),
          ),
        ],
      ),
    );
  }

  void _showLocationGuessDialog(BuildContext context, FirebaseService firebaseService, AppLocalizations l10n) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        title: Text(
          l10n.guessLocation,
          style: const TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              l10n.whereDoYouThink,
              style: const TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: ResponsiveUtils.getDialogHeight(context),
              width: double.maxFinite,
              child: ListView.builder(
                itemCount: LocationsData.locations.length,
                itemBuilder: (context, index) {
                  final location = LocationsData.locations[index];
                  return ListTile(
                    title: Text(
                      location.getName(l10n.localeName),
                      style: const TextStyle(color: Colors.white),
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      _guessLocation(firebaseService, location.id);
                    },
                  );
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.cancel),
          ),
        ],
      ),
    );
  }

  void _showExitDialog(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        title: Text(
          l10n.leaveGame,
          style: const TextStyle(color: Colors.white),
        ),
        content: Text(
          l10n.leaveGameConfirm,
          style: const TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.cancel),
          ),
          TextButton(
            onPressed: () {
              Navigator.popUntil(context, (route) => route.isFirst);
            },
            child: Text(l10n.leave),
          ),
        ],
      ),
    );
  }

  Future<void> _accusePlayer(FirebaseService firebaseService, String accusedId) async {
    try {
      await firebaseService.accusePlayer(widget.roomId, widget.playerId, accusedId);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.failedToAccusePlayer(e.toString())),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _vote(FirebaseService firebaseService, bool voteYes) async {
    try {
      await firebaseService.vote(widget.roomId, widget.playerId, voteYes);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to vote: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _guessLocation(FirebaseService firebaseService, String locationId) async {
    try {
      await firebaseService.guessLocation(widget.roomId, widget.playerId, locationId);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to guess location: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showAllLocationsDialog(BuildContext context, AppLocalizations l10n) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        title: Text(
          l10n.allLocations,
          style: const TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              l10n.viewSpyLocations,
              style: const TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: ResponsiveUtils.getDialogHeight(context),
              width: double.maxFinite,
              child: ListView.builder(
                itemCount: LocationsData.locations.length,
                itemBuilder: (context, index) {
                  final location = LocationsData.locations[index];

                  return Card(
                    color: const Color(0xFF0E4B99).withOpacity(0.3),
                    child: ListTile(
                      leading: const Icon(
                        Icons.location_on,
                        color: Color(0xFF0E4B99),
                      ),
                      title: Text(
                        location.getName(l10n.localeName),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      subtitle: Text(
                        location.getRoles(l10n.localeName).take(3).join(', ') +
                        (location.getRoles(l10n.localeName).length > 3 ? '...' : ''),
                        style: const TextStyle(color: Colors.white70, fontSize: 12),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.close),
          ),
        ],
      ),
    );
  }

  Future<void> _resetGameAndReturnToWaiting(FirebaseService firebaseService) async {
    try {
      await firebaseService.resetGameToWaiting(widget.roomId);

      if (mounted) {
        // Waiting room'a dön
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => WaitingRoomScreen(
              roomId: widget.roomId,
              playerId: widget.playerId,
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to reset game: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildGameEndContent(BuildContext context, GameRoom room, Player currentPlayer, FirebaseService firebaseService, AppLocalizations l10n) {
    // Determine winner
    final spy = room.players.where((p) => p.role == PlayerRole.spy).isNotEmpty
        ? room.players.where((p) => p.role == PlayerRole.spy).first
        : null;

    final spyWon = spy != null && spy.score > 0;
    final isGameEnd = room.state == GameState.gameEnd;
    final responsivePadding = ResponsiveUtils.getResponsivePadding(context);
    final spacing = ResponsiveUtils.getResponsiveSpacing(context);
    final buttonPadding = ResponsiveUtils.getButtonPadding(context);
    final isVerySmallScreen = ResponsiveUtils.isVerySmallScreen(context);

    return Column(
      children: [
        // Winner Announcement
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(isVerySmallScreen ? 16 : 24),
          decoration: BoxDecoration(
            color: spyWon ? const Color(0xFF8B0000) : const Color(0xFF0E4B99),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: (spyWon ? const Color(0xFF8B0000) : const Color(0xFF0E4B99)).withOpacity(0.3),
                blurRadius: 10,
                spreadRadius: 2,
              ),
            ],
          ),
          child: Column(
            children: [
              Icon(
                spyWon ? Icons.visibility_off : Icons.people,
                size: isVerySmallScreen ? 36 : 48,
                color: Colors.white,
              ),
              SizedBox(height: spacing),
              Text(
                spyWon ? l10n.spyWins : l10n.civiliansWin,
                style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: isVerySmallScreen ? 20 : null,
                ),
                textAlign: TextAlign.center,
              ),
              if (isGameEnd) ...[
                SizedBox(height: isVerySmallScreen ? 4 : 8),
                Text(
                  l10n.gameOver,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.white70,
                    fontSize: isVerySmallScreen ? 14 : null,
                  ),
                ),
              ],
            ],
          ),
        ),

        SizedBox(height: spacing),

        // Scores
        Expanded(
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.all(responsivePadding),
            decoration: BoxDecoration(
              color: const Color(0xFF16213E).withOpacity(0.5),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: const Color(0xFF0E4B99).withOpacity(0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  l10n.score,
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: Colors.white,
                    fontSize: isVerySmallScreen ? 18 : null,
                  ),
                ),
                SizedBox(height: spacing),
                Expanded(
                  child: ListView.builder(
                    itemCount: room.players.length,
                    itemBuilder: (context, index) {
                      final player = room.players[index];
                      final isSpy = player.role == PlayerRole.spy;

                      return Container(
                        margin: EdgeInsets.only(bottom: isVerySmallScreen ? 6 : 8),
                        padding: EdgeInsets.all(isVerySmallScreen ? 12 : 16),
                        decoration: BoxDecoration(
                          color: isSpy
                              ? const Color(0xFF8B0000).withOpacity(0.2)
                              : const Color(0xFF16213E),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: isSpy
                                ? const Color(0xFF8B0000)
                                : const Color(0xFF0E4B99).withOpacity(0.3),
                          ),
                        ),
                        child: Row(
                          children: [
                            CircleAvatar(
                              radius: isVerySmallScreen ? 14 : 20,
                              backgroundColor: isSpy
                                  ? const Color(0xFF8B0000)
                                  : const Color(0xFF0E4B99),
                              child: Text(
                                player.name[0].toUpperCase(),
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: isVerySmallScreen ? 10 : 14,
                                ),
                              ),
                            ),
                            SizedBox(width: isVerySmallScreen ? 8 : 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    player.name,
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600,
                                      fontSize: isVerySmallScreen ? 14 : null,
                                    ),
                                  ),
                                  Text(
                                    isSpy ? l10n.spy : l10n.civilian,
                                    style: TextStyle(
                                      color: isSpy
                                          ? const Color(0xFF8B0000)
                                          : const Color(0xFF0E4B99),
                                      fontSize: isVerySmallScreen ? 10 : 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Text(
                              '${player.score}',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: isVerySmallScreen ? 16 : 18,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),

        SizedBox(height: spacing),

        // Action Button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () => _resetGameAndReturnToWaiting(firebaseService),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF0E4B99),
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: buttonPadding),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              isGameEnd ? l10n.newGame : l10n.nextRound,
              style: TextStyle(
                fontSize: isVerySmallScreen ? 16 : 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

// Tamamen bağımsız Timer Widget - kendi state'ini yönetir
class GameTimerWidget extends StatefulWidget {
  final DateTime? endTime;
  final GameState gameState;

  const GameTimerWidget({
    super.key,
    this.endTime,
    required this.gameState,
  });

  @override
  State<GameTimerWidget> createState() => _GameTimerWidgetState();
}

class _GameTimerWidgetState extends State<GameTimerWidget> {
  Timer? _timer;
  Duration _timeRemaining = Duration.zero;
  DateTime? _lastEndTime;

  @override
  void initState() {
    super.initState();
    _startTimer();
  }

  @override
  void didUpdateWidget(GameTimerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.endTime != widget.endTime || oldWidget.gameState != widget.gameState) {
      _startTimer();
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _timer?.cancel();

    // Oyun bittiğinde timer'ı durdur
    if (widget.gameState == GameState.roundEnd ||
        widget.gameState == GameState.gameEnd ||
        widget.endTime == null) {
      setState(() {
        _timeRemaining = Duration.zero;
      });
      return;
    }

    // Sadece endTime değiştiğinde timer'ı yeniden başlat
    if (_lastEndTime == widget.endTime) return;
    _lastEndTime = widget.endTime;

    _updateTime();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      // Oyun durumu değiştiğinde timer'ı durdur
      if (widget.gameState == GameState.roundEnd ||
          widget.gameState == GameState.gameEnd) {
        timer.cancel();
        setState(() {
          _timeRemaining = Duration.zero;
        });
        return;
      }
      _updateTime();
    });
  }

  void _updateTime() {
    // Oyun bittiğinde timer'ı durdur
    if (widget.gameState == GameState.roundEnd ||
        widget.gameState == GameState.gameEnd ||
        widget.endTime == null) {
      setState(() {
        _timeRemaining = Duration.zero;
      });
      _timer?.cancel();
      return;
    }

    final now = DateTime.now();
    if (now.isAfter(widget.endTime!)) {
      setState(() {
        _timeRemaining = Duration.zero;
      });
      _timer?.cancel();
    } else {
      setState(() {
        _timeRemaining = widget.endTime!.difference(now);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final minutes = _timeRemaining.inMinutes;
    final seconds = _timeRemaining.inSeconds % 60;
    final timerPadding = ResponsiveUtils.getTimerPadding(context);
    final timerFontSize = ResponsiveUtils.getTimerFontSize(context);
    final isVerySmallScreen = ResponsiveUtils.isVerySmallScreen(context);

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(timerPadding),
      decoration: BoxDecoration(
        color: const Color(0xFF16213E),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _timeRemaining.inMinutes < 2 ? Colors.red : const Color(0xFF0E4B99),
          width: 2,
        ),
      ),
      child: Column(
        children: [
          Text(
            AppLocalizations.of(context)!.timeRemainingLabel,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.white60,
              fontSize: isVerySmallScreen ? 11 : 13,
            ),
          ),
          SizedBox(height: isVerySmallScreen ? 2 : 4),
          Text(
            '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}',
            style: Theme.of(context).textTheme.headlineLarge?.copyWith(
              color: _timeRemaining.inMinutes < 2 ? Colors.red : Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: timerFontSize,
            ),
          ),
        ],
      ),
    );
  }
}
