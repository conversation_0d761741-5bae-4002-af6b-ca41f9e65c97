import 'package:firebase_analytics/firebase_analytics.dart';

class AnalyticsService {
  static final AnalyticsService _instance = AnalyticsService._internal();
  factory AnalyticsService() => _instance;
  AnalyticsService._internal();

  late FirebaseAnalytics _analytics;

  void initialize(FirebaseAnalytics analytics) {
    _analytics = analytics;
  }

  FirebaseAnalytics get analytics => _analytics;

  // Custom Events
  Future<void> logCustomEvent(String name, {Map<String, Object>? parameters}) async {
    try {
      await _analytics.logEvent(
        name: name,
        parameters: parameters,
      );
      print('📊 Analytics Event: $name with parameters: $parameters');
    } catch (e) {
      print('❌ Analytics error: $e');
    }
  }

  // Game Events
  Future<void> logGameCreated({
    required String roomCode,
    required int playerCount,
  }) async {
    await logCustomEvent('game_created', parameters: {
      'room_code': roomCode,
      'player_count': playerCount,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  Future<void> logGameJoined({
    required String roomCode,
    required int playerCount,
  }) async {
    await logCustomEvent('game_joined', parameters: {
      'room_code': roomCode,
      'player_count': playerCount,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  Future<void> logGameStarted({
    required String roomCode,
    required int playerCount,
    required String location,
  }) async {
    await logCustomEvent('game_started', parameters: {
      'room_code': roomCode,
      'player_count': playerCount,
      'location': location,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  Future<void> logGameEnded({
    required String roomCode,
    required bool spyWins,
    required String reason,
    required int roundNumber,
    required int gameDurationSeconds,
  }) async {
    await logCustomEvent('game_ended', parameters: {
      'room_code': roomCode,
      'spy_wins': spyWins,
      'reason': reason,
      'round_number': roundNumber,
      'game_duration_seconds': gameDurationSeconds,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  Future<void> logPlayerAccused({
    required String roomCode,
    required String accuserRole,
    required String accusedRole,
    required int roundNumber,
  }) async {
    await logCustomEvent('player_accused', parameters: {
      'room_code': roomCode,
      'accuser_role': accuserRole,
      'accused_role': accusedRole,
      'round_number': roundNumber,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  Future<void> logVoteCast({
    required String roomCode,
    required String voterRole,
    required bool voteYes,
    required int roundNumber,
  }) async {
    await logCustomEvent('vote_cast', parameters: {
      'room_code': roomCode,
      'voter_role': voterRole,
      'vote_yes': voteYes,
      'round_number': roundNumber,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  Future<void> logLocationGuessed({
    required String roomCode,
    required String guessedLocation,
    required String actualLocation,
    required bool correct,
    required int roundNumber,
  }) async {
    await logCustomEvent('location_guessed', parameters: {
      'room_code': roomCode,
      'guessed_location': guessedLocation,
      'actual_location': actualLocation,
      'correct': correct,
      'round_number': roundNumber,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  // Screen Events
  Future<void> logScreenView(String screenName) async {
    await logCustomEvent('screen_view', parameters: {
      'screen_name': screenName,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  // User Properties
  Future<void> setUserProperty(String name, String value) async {
    try {
      await _analytics.setUserProperty(name: name, value: value);
      print('📊 Analytics User Property: $name = $value');
    } catch (e) {
      print('❌ Analytics user property error: $e');
    }
  }

  Future<void> setUserLanguage(String language) async {
    await setUserProperty('user_language', language);
  }

  Future<void> setUserGameExperience(String level) async {
    await setUserProperty('game_experience', level);
  }

  // Predefined Firebase Events
  Future<void> logAppOpen() async {
    await logCustomEvent('app_open');
  }

  Future<void> logSelectContent({
    required String contentType,
    required String itemId,
  }) async {
    await logCustomEvent('select_content', parameters: {
      'content_type': contentType,
      'item_id': itemId,
    });
  }

  Future<void> logShare({
    required String contentType,
    required String itemId,
    required String method,
  }) async {
    await logCustomEvent('share', parameters: {
      'content_type': contentType,
      'item_id': itemId,
      'method': method,
    });
  }
}
