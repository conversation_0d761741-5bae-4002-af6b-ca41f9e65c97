import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'services/firebase_service.dart';
import 'services/analytics_service.dart';
import 'services/locale_provider.dart';
import 'screens/home_screen.dart';
import 'l10n/app_localizations.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await FirebaseService().initialize();
  runApp(const SpyOutApp());
}

class SpyOutApp extends StatelessWidget {
  const SpyOutApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        Provider<FirebaseService>(
          create: (_) => FirebaseService(),
        ),
        Provider<FirebaseAnalytics>(
          create: (_) => FirebaseService().analytics,
        ),
        Provider<AnalyticsService>(
          create: (_) => AnalyticsService(),
        ),
        ChangeNotifierProvider<LocaleProvider>(
          create: (_) => LocaleProvider(),
        ),
      ],
      child: Consumer2<LocaleProvider, FirebaseAnalytics>(
        builder: (context, localeProvider, analytics, child) {
          return MaterialApp(
            title: 'SpyOut',
            locale: localeProvider.locale,
            navigatorObservers: [
              FirebaseAnalyticsObserver(analytics: analytics),
            ],
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('en'),
              Locale('tr'),
            ],
            theme: ThemeData(
              colorScheme: ColorScheme.fromSeed(
                seedColor: const Color(0xFF1A1A2E),
                brightness: Brightness.dark,
              ),
              useMaterial3: true,
              scaffoldBackgroundColor: const Color(0xFF0F0F23),
            ),
            home: const HomeScreen(),
          );
        },
      ),
    );
  }
}


