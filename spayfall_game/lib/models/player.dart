class Player {
  final String id;
  final String name;
  final bool isHost;
  final bool isOnline;
  final PlayerRole? role;
  final String? specificRole;
  final int score;
  final bool hasAccused;
  final bool hasVoted;

  const Player({
    required this.id,
    required this.name,
    this.isHost = false,
    this.isOnline = true,
    this.role,
    this.specificRole,
    this.score = 0,
    this.hasAccused = false,
    this.hasVoted = false,
  });

  Player copyWith({
    String? id,
    String? name,
    bool? isHost,
    bool? isOnline,
    PlayerRole? role,
    String? specificRole,
    int? score,
    bool? hasAccused,
    bool? hasVoted,
  }) {
    return Player(
      id: id ?? this.id,
      name: name ?? this.name,
      isHost: isHost ?? this.isHost,
      isOnline: isOnline ?? this.isOnline,
      role: role ?? this.role,
      specificRole: specificRole ?? this.specificRole,
      score: score ?? this.score,
      hasAccused: hasAccused ?? this.hasAccused,
      hasVoted: hasVoted ?? this.hasVoted,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'isHost': isHost,
      'isOnline': isOnline,
      'role': role?.toString(),
      'specificRole': specificRole,
      'score': score,
      'hasAccused': hasAccused,
      'hasVoted': hasVoted,
    };
  }

  factory Player.fromJson(Map<String, dynamic> json) {
    return Player(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      isHost: json['isHost'] ?? false,
      isOnline: json['isOnline'] ?? true,
      role: json['role'] != null ? PlayerRole.values.firstWhere(
        (e) => e.toString() == json['role'],
        orElse: () => PlayerRole.civilian,
      ) : null,
      specificRole: json['specificRole'],
      score: json['score'] ?? 0,
      hasAccused: json['hasAccused'] ?? false,
      hasVoted: json['hasVoted'] ?? false,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Player && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Player(id: $id, name: $name, role: $role, specificRole: $specificRole)';
  }
}

enum PlayerRole {
  civilian,
  spy,
}

extension PlayerRoleExtension on PlayerRole {
  String get displayName {
    switch (this) {
      case PlayerRole.civilian:
        return 'Civilian';
      case PlayerRole.spy:
        return 'Spy';
    }
  }

  String get displayNameTr {
    switch (this) {
      case PlayerRole.civilian:
        return 'Sivil';
      case PlayerRole.spy:
        return 'Casus';
    }
  }
}
