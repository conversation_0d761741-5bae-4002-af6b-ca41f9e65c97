class Location {
  final String id;
  final String nameEn;
  final String nameTr;
  final List<String> rolesEn;
  final List<String> rolesTr;

  const Location({
    required this.id,
    required this.nameEn,
    required this.nameTr,
    required this.rolesEn,
    required this.rolesTr,
  });

  String getName(String languageCode) {
    return languageCode == 'tr' ? nameTr : nameEn;
  }

  List<String> getRoles(String languageCode) {
    return languageCode == 'tr' ? rolesTr : rolesEn;
  }

  String getRandomRole(String languageCode) {
    final roles = getRoles(languageCode);
    roles.shuffle();
    return roles.first;
  }

  // Translate a role from English to the target language
  String translateRole(String englishRole, String languageCode) {
    if (languageCode != 'tr') return englishRole;

    final englishIndex = rolesEn.indexOf(englishRole);
    if (englishIndex != -1 && englishIndex < rolesTr.length) {
      return rolesTr[englishIndex];
    }
    return englishRole; // Return original if not found
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nameEn': nameEn,
      'nameTr': nameTr,
      'rolesEn': rolesEn,
      'rolesTr': rolesTr,
    };
  }

  factory Location.fromJson(Map<String, dynamic> json) {
    return Location(
      id: json['id'] ?? '',
      nameEn: json['nameEn'] ?? '',
      nameTr: json['nameTr'] ?? '',
      rolesEn: List<String>.from(json['rolesEn'] ?? []),
      rolesTr: List<String>.from(json['rolesTr'] ?? []),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Location && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Location(id: $id, nameEn: $nameEn, nameTr: $nameTr)';
  }
}
