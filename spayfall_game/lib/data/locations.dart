import '../models/location.dart';

class LocationsData {
  static final List<Location> locations = [
    Location(
      id: 'casino',
      nameEn: 'Casino',
      nameTr: '<PERSON><PERSON>',
      rolesEn: ['Dealer', 'Security Guard', '<PERSON><PERSON>', '<PERSON> Roller', 'Pit Boss', '<PERSON>ck<PERSON> Waitress'],
      rolesTr: ['<PERSON><PERSON><PERSON><PERSON>', 'Güvenlik Görevlisi', 'Barmen', 'Büyük Oyuncu', 'Masa Şefi', 'Kokteyl Garsonu'],
    ),
    Location(
      id: 'space_station',
      nameEn: 'Space Station',
      nameTr: '<PERSON><PERSON>y İstasyonu',
      rolesEn: ['Commander', 'Engineer', 'Scientist', 'Pilot', 'Doctor', 'Communications Officer'],
      rolesTr: ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON> İnsanı', 'Pilot', 'Do<PERSON>or', 'İletişim Subayı'],
    ),
    Location(
      id: 'restaurant',
      nameEn: 'Restaurant',
      nameTr: 'Restoran',
      rolesEn: ['Chef', 'Waiter', 'Customer', 'Manager', 'Dishwasher', 'Host'],
      rolesTr: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Müşteri', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'Karş<PERSON>layıcı'],
    ),
    Location(
      id: 'bank',
      nameEn: 'Bank',
      nameTr: 'Banka',
      rolesEn: ['Teller', 'Security Guard', 'Manager', 'Customer', 'Loan Officer', 'Vault Keeper'],
      rolesTr: ['Veznedar', 'Güvenlik Görevlisi', 'Müdür', 'Müşteri', 'Kredi Uzmanı', 'Kasa Sorumlusu'],
    ),
    Location(
      id: 'pirate_ship',
      nameEn: 'Pirate Ship',
      nameTr: 'Korsan Gemisi',
      rolesEn: ['Captain', 'First Mate', 'Cook', 'Gunner', 'Sailor', 'Lookout'],
      rolesTr: ['Kaptan', 'Kaptan Yardımcısı', 'Aşçı', 'Topçu', 'Denizci', 'Gözcü'],
    ),
    Location(
      id: 'hospital',
      nameEn: 'Hospital',
      nameTr: 'Hastane',
      rolesEn: ['Doctor', 'Nurse', 'Patient', 'Surgeon', 'Receptionist', 'Paramedic'],
      rolesTr: ['Doktor', 'Hemşire', 'Hasta', 'Cerrah', 'Resepsiyon', 'Paramedik'],
    ),
    Location(
      id: 'university',
      nameEn: 'University',
      nameTr: 'Üniversite',
      rolesEn: ['Professor', 'Student', 'Dean', 'Librarian', 'Teaching Assistant', 'Janitor'],
      rolesTr: ['Profesör', 'Öğrenci', 'Dekan', 'Kütüphaneci', 'Asistan', 'Temizlik Görevlisi'],
    ),
    Location(
      id: 'movie_studio',
      nameEn: 'Movie Studio',
      nameTr: 'Film Stüdyosu',
      rolesEn: ['Director', 'Actor', 'Cameraman', 'Producer', 'Sound Engineer', 'Makeup Artist'],
      rolesTr: ['Yönetmen', 'Oyuncu', 'Kameraman', 'Yapımcı', 'Ses Teknisyeni', 'Makyöz'],
    ),
    Location(
      id: 'embassy',
      nameEn: 'Embassy',
      nameTr: 'Elçilik',
      rolesEn: ['Ambassador', 'Security Officer', 'Translator', 'Diplomat', 'Secretary', 'Visitor'],
      rolesTr: ['Büyükelçi', 'Güvenlik Subayı', 'Tercüman', 'Diplomat', 'Sekreter', 'Ziyaretçi'],
    ),
    Location(
      id: 'military_base',
      nameEn: 'Military Base',
      nameTr: 'Askeri Üs',
      rolesEn: ['General', 'Soldier', 'Medic', 'Engineer', 'Intelligence Officer', 'Cook'],
      rolesTr: ['General', 'Asker', 'Sağlıkçı', 'Mühendis', 'İstihbarat Subayı', 'Aşçı'],
    ),
    Location(
      id: 'police_station',
      nameEn: 'Police Station',
      nameTr: 'Polis Karakolu',
      rolesEn: ['Detective', 'Police Officer', 'Chief', 'Suspect', 'Lawyer', 'Dispatcher'],
      rolesTr: ['Dedektif', 'Polis Memuru', 'Komiser', 'Şüpheli', 'Avukat', 'Santral Operatörü'],
    ),
    Location(
      id: 'cruise_ship',
      nameEn: 'Cruise Ship',
      nameTr: 'Gemi Seyahati',
      rolesEn: ['Captain', 'Passenger', 'Entertainer', 'Bartender', 'Chef', 'Lifeguard'],
      rolesTr: ['Kaptan', 'Yolcu', 'Eğlence Sorumlusu', 'Barmen', 'Şef', 'Cankurtaran'],
    ),
    Location(
      id: 'airplane',
      nameEn: 'Airplane',
      nameTr: 'Uçak',
      rolesEn: ['Pilot', 'Flight Attendant', 'Passenger', 'Co-pilot', 'Air Marshal', 'Mechanic'],
      rolesTr: ['Pilot', 'Hostes', 'Yolcu', 'Yardımcı Pilot', 'Hava Polisi', 'Teknisyen'],
    ),
    Location(
      id: 'school',
      nameEn: 'School',
      nameTr: 'Okul',
      rolesEn: ['Teacher', 'Student', 'Principal', 'Janitor', 'Librarian', 'Nurse'],
      rolesTr: ['Öğretmen', 'Öğrenci', 'Müdür', 'Hademe', 'Kütüphaneci', 'Hemşire'],
    ),
    Location(
      id: 'hotel',
      nameEn: 'Hotel',
      nameTr: 'Otel',
      rolesEn: ['Manager', 'Guest', 'Receptionist', 'Bellhop', 'Housekeeper', 'Concierge'],
      rolesTr: ['Müdür', 'Misafir', 'Resepsiyon', 'Bellboy', 'Kat Görevlisi', 'Kapıcı'],
    ),
  ];

  static Location getRandomLocation() {
    final shuffled = List<Location>.from(locations);
    shuffled.shuffle();
    return shuffled.first;
  }

  static Location? getLocationById(String id) {
    try {
      return locations.firstWhere((location) => location.id == id);
    } catch (e) {
      return null;
    }
  }
}
